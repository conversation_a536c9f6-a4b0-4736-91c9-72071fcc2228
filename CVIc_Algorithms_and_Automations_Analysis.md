# CVIc Algorithms and Automations: Complete Technical Analysis

**Verified and Accurate - Based on Code Analysis**

## **1. Geospatial Data Processing Algorithms**

### **1.1 Satellite Image Processing Pipeline**
```
Algorithm: processSatelliteImage()
Input: GeoTIFF file (up to 1GB)
Process:
1. File validation (type, size, extension)
2. ArrayBuffer extraction from File object
3. GeoTIFF metadata parsing using 'geotiff' library
4. Coordinate system detection and bounds calculation
5. Sentinel-2 specific information extraction via regex patterns
6. UTM zone detection and automatic bounds assignment
7. GeoRaster object creation for web rendering
8. Pixel data sampling and min/max calculation
9. Object URL generation for browser display

Output: ProcessedImage object with georeferencing data
```

### **1.2 Coordinate System Transformations**
```
Algorithm: extractBoundsFromGeoTIFF()
Process:
1. Parse GeoTIFF using fromArrayBuffer()
2. Extract ModelPixelScale and ModelTiepoint metadata
3. Calculate bounds: [xMin, yMin, xMax, yMax]
   - xMin = originX
   - yMax = originY
   - xMax = originX + width * scaleX
   - yMin = originY - height * scaleY
4. Validate coordinate ranges and geometric validity
5. Fallback to predefined UTM zone bounds for Sentinel-2

Coordinate Systems Supported:
- WGS84 (EPSG:4326)
- Web Mercator (EPSG:3857)
- UTM Zones 1-60 North (EPSG:32601-32660)
- UTM Zones 1-60 South (EPSG:32701-32760)
```

### **1.3 Sentinel-2 Metadata Extraction**
```
Algorithm: extractSentinelInfo()
Input: Filename string
Regex Patterns:
- UTM Zone: /T(\d{2})[A-Z]{3}/
- Date: /(\d{8})T\d{6}/
- Band: /B(\d{2})/
- Satellite: /S2[AB]/
- Resolution: /(\d+)m/

Process:
1. Pattern matching for metadata extraction
2. UTM zone calculation from longitude: floor((lng + 180) / 6) + 1
3. Date formatting: YYYY-MM-DD
4. Product type detection (TCI, False Color, etc.)

Output: Structured metadata object
```

## **2. Shapefile Processing Algorithms**

### **2.1 Shapefile Validation and Parsing**
```
Algorithm: processShapefile()
Input: ZIP file containing .shp, .shx, .dbf
Process:
1. File validation (size < 50MB, ZIP format)
2. ArrayBuffer extraction and parsing via 'shpjs' library
3. GeoJSON conversion and structure validation
4. Coordinate validation: lon ∈ [-180,180], lat ∈ [-90,90]
5. Geometry type validation (LineString, MultiLineString only)
6. Property sanitization (remove sensitive fields)

Validation Rules:
- Must be FeatureCollection
- Features must have LineString/MultiLineString geometry
- All coordinates must be valid geographic coordinates
- No empty feature arrays allowed
```

## **3. Shoreline Segmentation Algorithms**

### **3.1 Adaptive Segmentation Algorithm**
```
Algorithm: segmentShoreline()
Input: FeatureCollection<LineString|MultiLineString>, resolution (meters)
Process:
1. Extract LineString geometries from MultiLineString features
2. Calculate total line length using Turf.js: turf.length()
3. Determine optimal segment count: ceil(length / resolution)
4. Calculate actual resolution: length / numSegments
5. Generate segments using turf.lineSliceAlong()
6. Assign unique IDs and calculate individual segment lengths

Mathematical Formula:
- numSegments = ⌈totalLength / targetResolution⌉
- actualResolution = totalLength / numSegments
- segmentStart = i × actualResolution
- segmentEnd = min((i+1) × actualResolution, totalLength)

Output: Array of ShorelineSegment objects with geometry and properties
```

## **4. Vulnerability Index Calculation Algorithms**

### **4.1 Traditional CVI Formula**
```
Algorithm: calculateTraditional()
Mathematical Formula: CVI = √(∏Vi / n)
Process:
1. Validate equal weights (tolerance: 1e-6)
2. Calculate product of vulnerability values
3. Apply square root of (product / parameter_count)
4. Handle edge cases: prevent log(0) with minimum value 1e-9

Implementation:
product = 1.0
for i in values:
    product *= max(1e-9, values[i])
result = sqrt(product / n)
```

### **4.2 Geometric Mean Formula**
```
Algorithm: calculateGeometricMean()
Mathematical Formula: CVI = ∏(Vi^Wi)
Process:
1. Calculate weighted product of vulnerability values
2. Each value raised to its respective weight
3. Handle zero values with minimum threshold

Implementation:
product = 1.0
for i in range(len(values)):
    value = max(1e-9, values[i])
    product *= pow(value, weights[i])
```

### **4.3 Arithmetic Mean Formula**
```
Algorithm: calculateArithmeticMean()
Mathematical Formula: CVI = Σ(Vi × Wi)
Process:
1. Calculate weighted sum of vulnerability values
2. Direct multiplication of values by weights

Implementation:
weightedSum = 0.0
for i in range(len(values)):
    weightedSum += values[i] * weights[i]
```

### **4.4 Composite Index Algorithms**

#### **ICVI (Integrated Coastal Vulnerability Index)**
```
Algorithm: calculateICVIComposite()
Components:
- Environmental Vulnerability Index (EVI): 6 parameters
- Socioeconomic Vulnerability Index (SVI): 6 parameters

Process:
1. Separate parameters into EVI and SVI groups
2. Calculate EVI using arithmetic mean: Σ(Vi)/n
3. Calculate SVI using arithmetic mean: Σ(Vi)/n
4. Combine: ICVI = (EVI + SVI) / 2

Mathematical Formula:
EVI = (Σ environmental_parameters) / 6
SVI = (Σ socioeconomic_parameters) / 6
ICVI = (EVI + SVI) / 2
```

#### **CVI-SE (Socioeconomic Enhanced CVI)**
```
Algorithm: calculateCVISE()
Components:
- Physical CVI: Traditional geometric mean
- Socioeconomic VI: Arithmetic mean

Process:
1. Separate physical and socioeconomic parameters
2. Physical: √(∏Vi/n) for physical parameters
3. Socioeconomic: Σ(Vi)/n for socioeconomic parameters
4. Weighted combination: 60% Physical + 40% Socioeconomic

Mathematical Formula:
Physical_CVI = √(∏ physical_params / n_physical)
Socio_VI = Σ(socio_params) / n_socio
CVI_SE = 0.6 × Physical_CVI + 0.4 × Socio_VI
```

## **5. Parameter Assignment Automation**

### **5.1 Value Assignment Algorithm**
```
Algorithm: applyParameterValueToSegments()
Input: segments[], selectedSegmentIds[], parameter, value, vulnerability
Process:
1. Iterate through all segments
2. Check if segment ID is in selection
3. Create ParameterValue object (numerical/categorical)
4. Compare with existing values to detect changes
5. Update segment properties and parameters
6. Store updated segments in IndexedDB if changes occurred

Optimization: Only updates database if actual changes detected
```

### **5.2 Vulnerability Mapping Algorithm**
```
Algorithm: getCviCategory() / getCviRank()
Input: CVI score, formula type
Process:
1. Detect formula type (traditional 1-5 scale vs ICVI 0-1 scale)
2. Apply appropriate classification ranges
3. Map scores to vulnerability categories

Classification Ranges:
Traditional (1-5): Very Low(1), Low(2), Moderate(3), High(4), Very High(5)
ICVI (0-1): Very Low(0-0.2), Low(0.2-0.4), Moderate(0.4-0.6), High(0.6-0.8), Very High(0.8-1.0)
```

## **6. Statistical Analysis Algorithms**

### **6.1 Moving Average Calculation**
```
Algorithm: generateVulnerabilityProfileData()
Process:
1. Sort segments by index
2. Calculate adaptive window size: min(5, max(3, floor(segments.length/10)))
3. Apply centered moving average with boundary handling

Implementation:
windowSize = min(5, max(3, floor(segments.length / 10)))
for i in range(segments.length):
    start = max(0, i - floor(windowSize/2))
    end = min(segments.length-1, i + floor(windowSize/2))
    sum = Σ(scores[start:end])
    movingAvg[i] = sum / (end - start + 1)
```

### **6.2 Category Distribution Analysis**
```
Algorithm: generateCategoryDistributionData()
Process:
1. Count segments in each vulnerability category
2. Calculate percentages
3. Generate color-coded distribution data

Categories: Very Low, Low, Moderate, High, Very High
Colors: #1a9850, #91cf60, #fee08b, #fc8d59, #d73027
```

## **7. Export and Visualization Algorithms**

### **7.1 Map Capture Algorithm**
```
Algorithm: captureMapAsImage()
Process:
1. Detect Leaflet map instance
2. Disable animations and interactions
3. Apply CSS styles for clean capture
4. Use html2canvas with optimized settings:
   - Scale: 2 (high resolution)
   - CORS: enabled
   - Background: transparent
   - Timeout: 30 seconds
5. Convert canvas to PNG data URL
6. Restore original map state

Fallback: Simple DOM capture if Leaflet-specific capture fails
```

### **7.2 Report Generation Algorithm**
```
Algorithm: generateHtmlReport()
Input: segments, parameters, statistics, formula, mapImage
Process:
1. Generate statistical summaries
2. Create Chart.js compatible data structures
3. Embed map image as base64 data URL
4. Generate complete HTML with embedded CSS and JavaScript
5. Include interactive charts and detailed parameter tables

Output: Self-contained HTML file with embedded visualizations
```

## **8. Data Validation and Quality Control**

### **8.1 Coordinate Validation**
```
Algorithm: validateCoordinate()
Rules:
- Longitude: -180 ≤ lon ≤ 180
- Latitude: -90 ≤ lat ≤ 90
- Must be finite numbers
- Array length ≥ 2
```

### **8.2 Parameter Validation**
```
Algorithm: Index-specific validation rules
Process:
1. Parameter count validation
2. Weight sum validation (equal weights for traditional indices)
3. Formula compatibility checks
4. Required parameter presence validation

Example - CVI Validation:
- Exactly 6 parameters required
- Equal weights (1/6 each, tolerance: 0.001)
- Geometric mean formula compatibility
```

## **9. Performance Optimizations**

### **9.1 Lazy Loading and Caching**
- Database connections established on-demand
- GeoRaster objects cached for map rendering
- Object URL cleanup for memory management
- Sampling algorithms for large dataset statistics

### **9.2 Computational Efficiency**
- Direct key-based IndexedDB lookups (O(1))
- Batch operations for multiple segment updates
- Optimistic UI updates with database synchronization
- Statistical sampling for min/max calculations on large rasters

## **10. Error Handling and Fault Tolerance**

### **10.1 Graceful Degradation**
- Multiple fallback methods for coordinate extraction
- Alternative bounds calculation for corrupted GeoTIFF files
- Default coordinate systems for ungeoreferenced imagery
- Automatic retry mechanisms for transient failures

### **10.2 Data Integrity**
- Atomic IndexedDB transactions
- Validation checks before storage operations
- Rollback capability for failed operations
- Comprehensive error logging and user feedback

## **11. Advanced Geospatial Algorithms**

### **11.1 UTM Zone Detection Algorithm**
```
Algorithm: detectUTMZone()
Input: Longitude coordinate
Mathematical Formula: zone = floor((longitude + 180) / 6) + 1
Process:
1. Normalize longitude to [-180, 180] range
2. Apply zone calculation formula
3. Handle special cases for Norway and Svalbard
4. Return zone number (1-60)

Special Cases:
- Norway: Zones 31-33 have irregular boundaries
- Svalbard: Uses zones 33, 35, 37 instead of calculated zones
```

### **11.2 Bounds Calculation with Fallback**
```
Algorithm: calculateBoundsWithFallback()
Primary Method: GeoTIFF metadata extraction
Fallback Methods:
1. Sentinel-2 UTM zone lookup from predefined bounds table
2. European bounds default: [2.5, 35.0, 45.0, 71.5]
3. Global bounds: [-180, -90, 180, 90]

UTM Zone Bounds Table (60 zones):
Zone 32: [6.0, 0.0, 12.0, 84.0]  // Example for Central Europe
Zone 33: [12.0, 0.0, 18.0, 84.0] // Example for Eastern Europe
```

### **11.3 Pixel Sampling Algorithm**
```
Algorithm: samplePixelData()
Input: GeoRaster pixel data array
Process:
1. Determine sampling strategy based on array size
2. For large arrays (>1M pixels): systematic sampling every nth pixel
3. For small arrays: full scan
4. Calculate min/max values for visualization scaling
5. Handle NoData values and invalid pixels

Sampling Rate Calculation:
if (pixelCount > 1000000):
    sampleRate = max(100, pixelCount / 10000)
else:
    sampleRate = 1  // Full scan
```

## **12. Interactive Selection Algorithms**

### **12.1 Spatial Selection Algorithm**
```
Algorithm: selectSegmentsInBounds()
Input: Geographic bounds, segment collection
Process:
1. Convert bounds to geographic coordinates
2. Iterate through all segments
3. Check if segment geometry intersects with bounds
4. Use Turf.js booleanIntersects() for geometric testing
5. Return array of intersecting segment IDs

Geometric Test:
for segment in segments:
    if turf.booleanIntersects(segment.geometry, boundsPolygon):
        selectedIds.append(segment.id)
```

### **12.2 Bulk Parameter Assignment**
```
Algorithm: applyBulkParameterAssignment()
Input: selectedSegmentIds[], parameterValues[]
Process:
1. Validate parameter compatibility with selected index
2. Create parameter value objects for each assignment
3. Batch update segments in memory
4. Single IndexedDB transaction for all updates
5. Update UI with new parameter values

Optimization: Single database write for multiple segment updates
```

## **13. Vulnerability Classification Systems**

### **13.1 Multi-Scale Classification**
```
Traditional CVI Scale (1-5):
- Very Low: 1.0-1.8
- Low: 1.8-2.6
- Moderate: 2.6-3.4
- High: 3.4-4.2
- Very High: 4.2-5.0

ICVI Scale (0-1):
- Very Low: 0.0-0.2
- Low: 0.2-0.4
- Moderate: 0.4-0.6
- High: 0.6-0.8
- Very High: 0.8-1.0

Color Mapping:
#1a9850 (Very Low) → #91cf60 (Low) → #fee08b (Moderate) → #fc8d59 (High) → #d73027 (Very High)
```

### **13.2 Parameter Ranking Tables**
```
Algorithm: getRankingValue()
Input: parameter value, ranking table
Process:
1. Iterate through ranking table entries
2. For numerical parameters: check if value falls within criteria range
3. For categorical parameters: exact string matching
4. Return vulnerability score (1-5 or 0.1-0.9 depending on index)
5. Handle edge cases and invalid values

Range Parsing Examples:
">2.0 m/yr" → value > 2.0
"1.0-2.0 m/yr" → 1.0 ≤ value ≤ 2.0
"<1.8 mm/yr" → value < 1.8
```

## **14. Memory Management Algorithms**

### **14.1 Object URL Lifecycle Management**
```
Algorithm: manageObjectURLs()
Process:
1. Create: URL.createObjectURL(blob) for satellite images
2. Store: URL reference in ProcessedImage object
3. Use: Reference URL for map layer rendering
4. Cleanup: URL.revokeObjectURL() when image removed
5. Garbage Collection: Automatic browser cleanup of revoked URLs

Memory Optimization:
- Immediate cleanup when images are removed
- Prevents memory leaks from accumulated blob URLs
- Maintains performance with large satellite images
```

### **14.2 Large Dataset Handling**
```
Algorithm: handleLargeDatasets()
Strategies:
1. Streaming processing for GeoTIFF files
2. Chunked reading of large ArrayBuffers
3. Progressive loading of segment collections
4. Lazy initialization of map layers
5. Viewport-based rendering optimization

Thresholds:
- File size limit: 1GB for satellite images
- Segment count optimization: >1000 segments use clustering
- Memory usage monitoring: Track IndexedDB quota usage
```

## **15. Export Format Algorithms**

### **15.1 GeoJSON Export**
```
Algorithm: exportToGeoJSON()
Process:
1. Create FeatureCollection structure
2. Include all segment geometries with properties
3. Embed parameter values and vulnerability scores
4. Add metadata: timestamp, index type, formula
5. Validate GeoJSON specification compliance

Output Structure:
{
  "type": "FeatureCollection",
  "metadata": { indexType, formula, timestamp },
  "features": [
    {
      "type": "Feature",
      "geometry": { LineString coordinates },
      "properties": {
        id, length, parameters, vulnerabilityIndex, category
      }
    }
  ]
}
```

### **15.2 CSV Export Algorithm**
```
Algorithm: exportToCSV()
Process:
1. Extract tabular data from segments
2. Flatten nested parameter objects
3. Generate column headers dynamically
4. Format numerical values with appropriate precision
5. Handle special characters and escaping
6. Create downloadable CSV blob

Column Structure:
ID, Length, Parameter1_Value, Parameter1_Vulnerability, ..., CVI_Score, Category
```

This comprehensive analysis provides the complete technical foundation for your software paper, covering all algorithms and automations engineered in the CVIc application.
