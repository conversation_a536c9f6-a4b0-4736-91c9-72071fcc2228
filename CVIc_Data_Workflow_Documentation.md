# CVIc Complete Data Workflow: Storage and Retrieval Operations

**Verified and Accurate - Based on Code Analysis**

## **Step 1: Satellite Image Upload** (if user chooses digitization path)

**User Action**: Upload GeoTIFF satellite images

**Data Operations**:
```js
STORE → IndexedDB.satelliteImages
Key: "image-{timestamp}-{random}"
Data: {
  id: "image-{timestamp}-{random}",
  name: "filename.tif",
  url: "blob:http://localhost:3000/...",
  bounds: [west, south, east, north],
  timestamp: 1234567890,
  georaster: <GeoRaster object>,
  arrayBuffer: <GeoTIFF binary data>,
  metadata: {
    size: 12345678,
    type: "image/tiff",
    isSentinel: true/false,
    isCOG: true/false,
    sentinelInfo: {
      utmZone: "32",
      date: "2023-06-15",
      band: "B08",
      satellite: "S2A",
      productType: "True Color Image"
    }
  }
}
```

## **Step 2A: Shoreline Upload** (shapefile path)

**User Action**: Upload shapefile

**Data Operations**:
```js
STORE → IndexedDB.shorelineData
Key: "current-shoreline"
Data: {
  id: "current-shoreline",
  data: {
    type: "FeatureCollection",
    features: [
      {
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: [[lon,lat], [lon,lat]...]
        },
        properties: {
          id: "feature_1",
          FID: 0,
          ...
        }
      }
    ]
  },
  timestamp: 1234567890
}
```

## **Step 2B: Shoreline Digitization** (satellite image path)

**User Action**: Draw shoreline on satellite images

**Data Operations**:
```js
RETRIEVE ← IndexedDB.satelliteImages (getAllSatelliteImages())

STORE → IndexedDB.shorelineData
Key: "digitized-shoreline"
Data: { GeoJSON FeatureCollection of drawn LineStrings }

STORE → IndexedDB.shorelineData
Key: "current-shoreline"
Data: { Same GeoJSON FeatureCollection }
```

## **Step 3: Shoreline Segmentation**

**User Action**: Generate shoreline segments

**Data Operations**:
```js
RETRIEVE ← IndexedDB.shorelineData["current-shoreline"]

STORE → IndexedDB.shorelineData
Key: "current-segments"
Data: {
  id: "current-segments",
  data: {
    type: "FeatureCollection",
    features: [
      {
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: [...]
        },
        properties: {
          id: "segment_1",
          length: 150.5,
          index: 0,
          lineIndex: 0
        }
      },
      { segment_2... },
      { segment_3... }
    ]
  },
  timestamp: 1234567890
}
```

## **Step 4: Parameter Selection**

**User Action**: Choose vulnerability index and parameters

**Data Operations**:
```js
STORE → IndexedDB.shorelineData
Key: "current-index"
Data: {
  id: "current-index",
  data: {
    type: "FeatureCollection",
    features: [
      {
        type: "Feature",
        properties: {
          type: "geometric-mean",
          name: "Coastal Vulnerability Index",
          description: "Traditional CVI calculation"
        }
      }
    ]
  },
  timestamp: 1234567890
}

STORE → IndexedDB.shorelineData
Key: "current-parameters"
Data: {
  id: "current-parameters",
  data: {
    type: "FeatureCollection",
    features: [
      {
        type: "Feature",
        properties: {
          id: "wave_height",
          name: "Significant Wave Height",
          type: "numerical",
          weight: 1,
          enabled: true,
          indexSpecificName: "Wave Height",
          rankingTable: [...]
        }
      },
      {
        type: "Feature",
        properties: {
          id: "coastal_slope",
          name: "Coastal Slope",
          type: "numerical",
          weight: 1,
          enabled: true,
          indexSpecificName: "Coastal Slope",
          rankingTable: [...]
        }
      }
    ]
  },
  timestamp: 1234567890
}
```

## **Step 5: Parameter Assignment**

**User Action**: Assign values to segments on map

**Data Operations**:
```js
RETRIEVE ← IndexedDB.shorelineData["current-segments"]
RETRIEVE ← IndexedDB.shorelineData["current-parameters"]
RETRIEVE ← IndexedDB.shorelineData["current-index"]

For each value assignment:
UPDATE → IndexedDB.shorelineData["current-segments"]
Data: {
  id: "current-segments",
  data: {
    type: "FeatureCollection",
    features: [
      {
        type: "Feature",
        geometry: { type: "LineString", coordinates: [...] },
        properties: {
          id: "segment_1",
          length: 150.5,
          index: 0,
          lineIndex: 0,
          parameters: {
            "wave_height": {
              type: "numerical",
              value: 2.5,
              vulnerability: 3
            },
            "coastal_slope": {
              type: "numerical",
              value: 0.8,
              vulnerability: 4
            }
          }
        }
      }
    ]
  },
  timestamp: 1234567890
}
```

## **Step 6: CVI Calculation**

**User Action**: Calculate vulnerability scores

**Data Operations**:
```js
RETRIEVE ← IndexedDB.shorelineData["current-segments"]

UPDATE → IndexedDB.shorelineData["current-segments"]
Data: {
  // Same structure as above, but with added CVI scores
  features: [
    {
      type: "Feature",
      properties: {
        id: "segment_1",
        parameters: { wave_height: {...}, coastal_slope: {...} },
        vulnerabilityIndex: 12.5,  // ← Calculated CVI score added
        vulnerabilityFormula: "geometric-mean"
      }
    }
  ]
}
```

## **Step 7: Results Display**

**User Action**: View final results

**Data Operations**:
```js
RETRIEVE ← IndexedDB.shorelineData["current-segments"]
RETRIEVE ← IndexedDB.shorelineData["current-parameters"]
```

## **IndexedDB Storage Structure Summary**

**Database**: `shorelineDB` (version 2)

**Object Store 1**: `shorelineData` (keyPath: 'id')
- `"current-shoreline"` → Original shoreline geometry
- `"digitized-shoreline"` → User-drawn shoreline (if applicable)
- `"current-segments"` → Segmented shoreline with parameters and CVI scores
- `"current-parameters"` → Selected vulnerability parameters
- `"current-index"` → Selected vulnerability index formula

**Object Store 2**: `satelliteImages` (keyPath: 'id')
- `"image-{timestamp}-{random}"` → Processed satellite images with bounds and metadata

## **Data Flow Characteristics**

1. **Linear Progression**: Each step stores data that the next step retrieves
2. **Key-Based Retrieval**: Consistent naming allows predictable data access
3. **Incremental Updates**: Data gets enriched at each step (segments → parameters → CVI scores)
4. **State Persistence**: All data remains in browser until manually cleared
5. **No Server Communication**: Everything stored and processed locally in IndexedDB
6. **Atomic Operations**: Uses IndexedDB transactions for data consistency
    
7. **Memory Management**: Object URLs created/destroyed for satellite images
8. **Type Safety**: TypeScript interfaces ensure data structure consistency

## **Technical Implementation Details**

- **Library**: Uses 'idb' (v8.0.2) Promise-based wrapper around IndexedDB
    - Native IndexedDB is callback-based (old-style JavaScript)
    - 'idb' converts it to Promise-based (modern async/await)
- **Serialization**: Native structured cloning handles complex objects
    - Structured Clone Algorithm (W3C standard) for serialization
    - Supports binary data: ArrayBuffers for satellite imagery
- **Error Handling**: Specific handling for QuotaExceededError
- **Lazy Initialization**: Database connections established on-demand
    - Faster application startup (No databse connection on app load)
    - Memory efficient
- **Singleton Pattern**: Single IndexedDBService instance across application
    - Prevents connection conflicts with all components using a shared state management
    - Memory efficient

## Data Storage Format
 - **Native JavaScript Objects**
    - The `data` field in `ShorelineStore` contains `GeoJSON FeatureCollection objects` as live JavaScript objects, not serialized JSON.
